{"private": true, "type": "module", "scripts": {"build": "vite build", "dev": "vite"}, "dependencies": {"@imacrayon/alpine-ajax": "^0.12.0", "@tailwindcss/vite": "^4.0.7", "alpinejs": "^3.14.8", "autoprefixer": "^10.4.20", "concurrently": "^9.0.1", "instant.page": "^5.2.0", "laravel-vite-plugin": "^1.0", "tailwindcss": "^4.0.7", "vite": "^6.0"}, "optionalDependencies": {"@rollup/rollup-linux-x64-gnu": "4.9.5", "@tailwindcss/oxide-linux-x64-gnu": "^4.0.1", "lightningcss-linux-x64-gnu": "^1.29.1"}, "devDependencies": {"@tailwindcss/forms": "^0.5.10", "@tailwindcss/typography": "^0.5.16"}}