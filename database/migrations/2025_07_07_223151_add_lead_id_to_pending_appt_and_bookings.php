<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('pending_appointments', function (Blueprint $table) {
            $table->integer('lead_id')->nullable();
        });

        Schema::table('bookings', function (Blueprint $table) {
            $table->integer('lead_id')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('pending_appointments', function (Blueprint $table) {
            $table->dropColumn('lead_id');
        });

        Schema::table('bookings', function (Blueprint $table) {
            $table->dropColumn('lead_id');
        });
    }
};
