<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('leads', function (Blueprint $table) {
            $table->string('form_type')->nullable();
            $table->string('company_name')->nullable();
            $table->string('job_title')->nullable();
            $table->string('department')->nullable();
            $table->string('other_condition')->nullable();
            $table->boolean('is_ever_visited')->default(0);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('leads', function (Blueprint $table) {
            $table->dropColumn('form_type');
            $table->dropColumn('company_name');
            $table->dropColumn('job_title');
            $table->dropColumn('department');
            $table->dropColumn('other_condition');
            $table->dropColumn('is_ever_visited');
        });
    }
};
