<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('leads', function (Blueprint $table) {
            $table->dropColumn('customer_type');
            $table->enum('customer_type', ["New Patient", "Re-Subscribers", "Upgrade Patient"])->default('New Patient');
        });
    }

    public function down(): void
    {
        Schema::table('leads', function (Blueprint $table) {
            $table->dropColumn('customer_type');
            $table->enum('customer_type', ["New Patient", "Re-Subscribe"])->default('New Patient')->change();
        });
    }
};
