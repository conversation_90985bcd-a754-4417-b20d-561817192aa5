<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('leads',  function(Blueprint $table){
            $table->string('nric', 12)->nullable();
            $table->string('leads_category', 20)->nullable();
            $table->enum('follow_up', ['FU 1','FU 2','FU 3'])->nullable();
            $table->date('follow_up_date')->nullable();
            $table->string('condition')->nullable();
            $table->string('address')->nullable();
            $table->enum('status',['Closed','Not Closed','In Progress','Pending'])->default('Pending');
            $table->enum('customer_type', ['New Patient','Re- Subscribers']);
            $table->enum('service_type', ['center','housecall'])->default('center');
            $table->string('package_closed')->nullable();
            $table->string('invoice_number')->nullable();
            $table->enum('payment', ['full', 'partial', 'panel', 'deposit'])->nullable();
            $table->decimal('amount')->default(0);
            $table->decimal('collection')->default(0);
            $table->date('appointment_date')->nullable();
        });
    }


    public function down(): void
    {
        Schema::table('leads',  function(Blueprint $table){
            $table->dropColumn('nric');
            $table->dropColumn('leads_category');
            $table->dropColumn('follow_up');
            $table->dropColumn('follow_up_date');
            $table->dropColumn('condition');
            $table->dropColumn('address');
            $table->dropColumn('status');
            $table->dropColumn('customer_type');
            $table->dropColumn('service_type');
            $table->dropColumn('package_closed');
            $table->dropColumn('invoice_number');
            $table->dropColumn('payment');
            $table->dropColumn('amount');
            $table->dropColumn('collection');
            $table->dropColumn('appointment_date');
        });
    }
};
