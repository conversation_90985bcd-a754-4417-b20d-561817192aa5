<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('leads',  function(Blueprint $table){
            $table->string('uploaded_file_reference', 255)->nullable();
            $table->string('receipt_number')->nullable();
            $table->dateTime('preferred_appointment_date_time')->nullable();
            $table->integer('preferred_therapist_id')->nullable();
        });
    }

    public function down(): void
    {
        Schema::table('leads',  function(Blueprint $table){
            $table->dropColumn('uploaded_file_reference');
            $table->dropColumn('receipt_number');
            $table->dropColumn('preferred_appointment_date_time');
            $table->dropColumn('preferred_therapist_id');
        });
    }
};
