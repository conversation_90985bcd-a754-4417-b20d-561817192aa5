@import 'tailwindcss';
@import './layout.css';
@import './modal.css';
@import './form.css';

@source "../views";
@source '../../vendor/laravel/framework/src/Illuminate/Pagination/resources/views/*.blade.php';

@custom-variant dark (&:where(.dark, .dark *));
@custom-variant aria-current (&[aria-current="page"]);


@theme {
    --font-sans: 'Instrument Sans', ui-sans-serif, system-ui, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';
    --color-accent: var(--color-gray-800);
    --color-accent-content: var(--color-gray-800);
    --color-accent-foreground: var(--color-white);
}

@layer theme {
    .dark {
        --color-accent: var(--color-white);
        --color-accent-content: var(--color-white);
        --color-accent-foreground: var(--color-gray-800);
    }
}
