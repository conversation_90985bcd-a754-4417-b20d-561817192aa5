dialog,
dialog::backdrop {
    opacity: 0;
    transition: all 0.075s allow-discrete;
}

dialog {
    background: var(--color-white);
    margin: auto;
    padding: calc(var(--spacing) * 6);
    max-width: var(--container-lg);
    box-shadow: var(--shadow-lg);
    border-radius: var(--radius-xl);
    border: 1px solid transparent;
    transform: scale(0.95);

    @variant dark {
        background: var(--color-gray-800);
        border-color: var(--color-gray-700);
    }
}

dialog[open],
dialog[open]::backdrop {
    opacity: 1;
    transform: translateX(0) scale(1);
    transition: all 0.15s allow-discrete;
}

@starting-style {

    dialog[open],
    dialog[open]::backdrop {
        opacity: 0;
    }

    dialog[open] {
        transform: scale(0.95);
    }
}

dialog::backdrop {
    background-color: rgba(0, 0, 0, 0.25);
}
