<?php

namespace App\Jobs;

use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Support\Facades\Log;
use \Spatie\WebhookClient\Jobs\ProcessWebhookJob;

class StoreFormB2BEventJob extends Process<PERSON>ebhookJob implements ShouldQueue
{
    use Queueable;

    public function handle(): void
    {
        // $this->webhookCall // contains an instance of `WebhookCall`
        Log::error($this->webhookCall);
    }
}
