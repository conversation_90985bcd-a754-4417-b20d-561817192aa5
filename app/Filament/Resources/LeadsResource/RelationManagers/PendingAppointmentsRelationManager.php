<?php

namespace App\Filament\Resources\LeadsResource\RelationManagers;

use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Widgets\StatsOverviewWidget\Stat;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class PendingAppointmentsRelationManager extends RelationManager
{
    protected static string $relationship = 'pendingAppointment';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Wizard::make()->schema([
                    Forms\Components\Wizard\Step::make('Choose Date')->schema([
                        Forms\Components\TextInput::make('lead_id')
                        ->required()
                        ->maxLength(255),
                    ])
                ])
                ->columnSpanFull(),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('lead_id')
            ->columns([
                Tables\Columns\TextColumn::make('lead_id'),
            ])
            ->filters([
                //
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make()
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function canViewForRecord(Model $ownerRecord, string $pageClass): bool
    {
        return $ownerRecord->status === 'Closed';
    }
}
