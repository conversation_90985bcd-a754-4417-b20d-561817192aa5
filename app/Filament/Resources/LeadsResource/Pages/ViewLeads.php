<?php

namespace App\Filament\Resources\LeadsResource\Pages;

use App\Filament\Resources\LeadsResource;
use App\Models\Customer;
use App\Models\PendingAppointment;
use App\User;
use Carbon\Carbon;
use Filament\Actions;
use Filament\Actions\Action;
use Filament\Resources\Pages\ViewRecord;

class ViewLeads extends ViewRecord
{
    protected static string $resource = LeadsResource::class;

     protected function getHeaderActions(): array
    {
        return [
            Actions\EditAction::make(),
            Action::make('goToExternal')
                ->label('Create Appointment')
                ->color('success')
                ->url(fn ($record) => config('app.main_system_url') . "bookings/stage-2/$record->pending_appointment_id")
                ->openUrlInNewTab()
                ->icon('heroicon-o-arrow-top-right-on-square')
                ->visible(fn () => $this->record->status === 'Closed'),
            Action::make('closeLead')
                ->label('Close Lead')
                ->icon('heroicon-o-check-circle')
                ->requiresConfirmation()
                ->color('success')
                ->action(function () {
                    $lead = $this->record;
                    $patient_id = $lead->customer_id;

                    // Create related customer
                    if(!$lead->is_existing_customer){
                        $user = User::create([
                            'name' => $lead->name,
                            'api_token' => \Illuminate\Support\Str::random(60),
                            'id_type' => '',
                            'id_no' => $lead->nric,
                            'email' => $lead->email,
                            'gender' => $lead->gender,
                            'mobile' => $lead->phone,
                            'dob' => '0001-01-01',
                            'address_1' => $lead->address,
                            'address_2' => '',
                            'state' => '',
                            'postcode' => '',
                            'city' => '',
                            'center_id' => auth()->user()->center_id,
                        ]);

                        $customer = Customer::create([
                            'user_id' => $user->id,
                            'condition' => $lead->condition,
                            'company' => $lead->company ?? '',
                            'medium' => $lead->medium ?? '',
                            'status' => 'Completed'
                        ]);

                        $patient_id = $customer->id;
                    }

                    $appointment = PendingAppointment::create([
                        'patient_id' => $patient_id,
                        'start_create' => Carbon::createFromTimestamp(now()),
                        'source' => $lead->resource,
                        'creator_id' => auth()->id(),
                        'clinic_id' => $lead->clinic_id ?? 0,
                        'lead_id' => $lead->id,
                    ]);

                    $lead->update([
                        'pending_appointment_id' => $appointment->id,
                        'closed_by' => auth()->id(),
                        'closed_at' => now(),
                        'status' => 'Closed'
                    ]);

                    // $this->notify('success', "Lead closed and Customer #{$customer->id} created.");
                })
                ->successRedirectUrl(fn () => static::getResource()::getUrl('view', ['record' => $this->record]))
                ->visible(fn () => $this->record->status !== 'Closed'),
        ];
    }

    public function saveAndClose(): void
    {
        // ...
    }
}
