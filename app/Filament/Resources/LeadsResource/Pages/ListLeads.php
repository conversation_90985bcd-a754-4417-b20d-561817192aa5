<?php

namespace App\Filament\Resources\LeadsResource\Pages;

use App\Filament\Resources\LeadsResource;
use App\Filament\Widgets\LeadsOverviewWidget;
use App\Models\Lead;
use Filament\Actions;
use Filament\Resources\Components\Tab;
use Filament\Resources\Pages\ListRecords;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

class ListLeads extends ListRecords
{
    protected static string $resource = LeadsResource::class;

    public Collection $orderByStatuses;
    public int $orderClosedByPerson;
    public function __construct()
    {
        $this->orderByStatuses = Lead::select('status', DB::raw('count(*) as lead_count'))
            ->groupBy('status')
            ->pluck('lead_count','status');

        $this->orderClosedByPerson = Lead::where('status', 'Closed')
                                            ->where('closed_by', auth()->id())
                                            ->count();
    }

    protected function getHeaderActions(): array
    {
        return [
            // \EightyNine\ExcelImport\ExcelImportAction::make()
            //     ->color("primary"),
            Actions\CreateAction::make(),
        ];
    }

    protected function getHeaderWidgets(): array
    {
        return [
            LeadsOverviewWidget::class,
        ];
    }

    public function getTabs(): array
    {
        return [
            'In progress' =>  Tab::make()
                    ->badge($this->orderByStatuses['In Progress'] ?? '0')
                    ->query(fn($query) => $query->where('status', 'In Progress')),
            'Pending' =>  Tab::make()
                    ->badge($this->orderByStatuses['Pending'] ?? '0')
                    ->query(fn($query) => $query->where('status', 'Pending')),
            'Not Closed' =>  Tab::make()
                    ->badge($this->orderByStatuses['Not Closed'] ?? '0')
                    ->query(fn($query) => $query->where('status', 'Not Closed')),
            'Closed' =>  Tab::make()
                    ->badge($this->orderClosedByPerson ?? '0')
                    ->query(fn($query) => $query->where('status', 'Closed')->where('closed_by', auth()->id())),
        ];
    }
}
