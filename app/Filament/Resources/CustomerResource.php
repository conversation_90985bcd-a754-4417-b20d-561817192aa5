<?php

namespace App\Filament\Resources;

use App\Filament\Resources\CustomerResource\Pages;
use App\Filament\Resources\CustomerResource\RelationManagers;
use App\Filament\Resources\CustomerResource\RelationManagers\BookingsRelationManager;
use App\Models\Booking;
use App\Models\Customer;
use Carbon\Carbon;
use Filament\Forms;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Malzariey\FilamentDaterangepickerFilter\Filters\DateRangeFilter;
use pxlrbt\FilamentExcel\Actions\Tables\ExportAction;
use pxlrbt\FilamentExcel\Exports\ExcelExport;

class CustomerResource extends Resource
{
    protected static ?string $model = Customer::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    protected static bool $shouldRegisterNavigation = false;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make("Customer Info")
                ->schema([
                    Forms\Components\Group::make()
                        ->relationship('user')
                        ->schema([
                            Forms\Components\TextInput::make('id_no')
                                ->required(),
                            Forms\Components\TextInput::make('name')
                                ->required(),
                            Forms\Components\Select::make('gender')
                                ->options([
                                    'male' => 'Male',
                                    'female' => 'Female'
                                ])
                                ->default('male')
                                ->required(),
                            Forms\Components\TextInput::make('email')
                                ->required(),
                            Forms\Components\TextInput::make('mobile')
                                ->required(),
                        ]),
                    Forms\Components\TextInput::make('status')
                        ->required()
                        ->maxLength(255),
                    Forms\Components\TextInput::make('company')
                        ->maxLength(255),
                ]),
                Forms\Components\Section::make()
                    ->schema([
                        Forms\Components\Placeholder::make('created_at')
                            ->label('Created at')
                            ->content(fn (Customer  $record): ?string => $record->created_at?->diffForHumans()),

                        Forms\Components\Placeholder::make('updated_at')
                            ->label('Last modified at')
                            ->content(fn (Customer  $record): ?string => $record->updated_at?->diffForHumans()),
                    ])
                    ->columnSpan(['lg' => 1])
                    ->hidden(fn (?Customer  $record) => $record === null),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('id')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('user.id_no')
                    ->label('IC NO'),
                Tables\Columns\TextColumn::make('user.name')
                    ->label('Name')
                    ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('user.gender')
                    ->label('Gender'),
                Tables\Columns\TextColumn::make('user.mobile')
                    ->label('WhatsApp')
                    ->formatStateUsing(fn ($state) => '+'.preg_replace('/\D/', '', $state)) // sanitize phone number
                    ->url(fn ($state) => "https://wa.me/" . preg_replace('/\D/', '', $state), true)
                    ->openUrlInNewTab()
                    ->color('success')
                    ->searchable(),
                Tables\Columns\TextColumn::make('latestBooking.date')
                    ->label('Latest Appointment')
                    ->date(),
                Tables\Columns\TextColumn::make('latestBooking.title')
                    ->label("Latest Appt Title")
                    ->url(fn ($record) => config('app.main_system_url') . "bookings/" . ($record->latestBooking ? $record->latestBooking->id : null))
                    ->color('success')
                    ->openUrlInNewTab(),
                Tables\Columns\TextColumn::make('total_booking')
                    ->label('Completed Session')
                    ->numeric(),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\IconColumn::make('follow_up')
                    ->boolean()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('medium')
                    ->searchable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                SelectFilter::make('user.gender')
                     ->label('Gender'),
                DateRangeFilter::make('bookings.date')->label('Appointment Date')
                    ->modifyQueryUsing(fn(Builder $query, ?Carbon $startDate , ?Carbon $endDate , $dateString) =>
                        $query->when(!empty($dateString),
                            fn (Builder $query, $date) : Builder =>
                                $query->whereHas('latestBooking', function (Builder $q) use ($startDate, $endDate) {
                                    $q->whereBetween('date', [$startDate, $endDate]);
                                })
                            ),
                    ),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->headerActions([
                ExportAction::make()
                    ->visible(fn () => auth()->user()?->hasRole('superadmin')),
            ]);
    }

    public static function getEloquentQuery(): \Illuminate\Database\Eloquent\Builder
    {
        return parent::getEloquentQuery()->with('user','latestBooking');
    }

    public static function getRelations(): array
    {
        return [
            BookingsRelationManager::class
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListCustomers::route('/'),
            // 'create' => Pages\CreateCustomer::route('/create'),
            'edit' => Pages\EditCustomer::route('/{record}/edit'),
            'view' => Pages\ViewCustomer::route('/{record}'),
        ];
    }
}
