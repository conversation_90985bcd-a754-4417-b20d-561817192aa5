<?php

namespace App\Filament\Resources\PendingAppointmentResource\Pages;

use App\Filament\Resources\PendingAppointmentResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListPendingAppointments extends ListRecords
{
    protected static string $resource = PendingAppointmentResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
