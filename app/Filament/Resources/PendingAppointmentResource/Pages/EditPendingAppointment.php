<?php

namespace App\Filament\Resources\PendingAppointmentResource\Pages;

use App\Filament\Resources\PendingAppointmentResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditPendingAppointment extends EditRecord
{
    protected static string $resource = PendingAppointmentResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\ViewAction::make(),
            Actions\DeleteAction::make(),
            Actions\ForceDeleteAction::make(),
            Actions\RestoreAction::make(),
        ];
    }
}
