<?php

namespace App\Filament\Resources;

use App\Filament\Resources\PendingAppointmentResource\Pages;
use App\Filament\Resources\PendingAppointmentResource\RelationManagers;
use App\Models\Center;
use App\Models\Customer;
use App\Models\PendingAppointment;
use App\Models\Therapist;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Forms\Get;
use Filament\Forms\Set;
use Filament\Resources\Resource;
use Filament\Support\Enums\Alignment;
use Filament\Support\Enums\IconPosition;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use JaOcero\RadioDeck\Forms\Components\RadioDeck;

class PendingAppointmentResource extends Resource
{
    protected static ?string $model = PendingAppointment::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Wizard::make([
                    Forms\Components\Wizard\Step::make('Appointment Date')->schema([
                        Forms\Components\TextInput::make('patient_id')->required()->numeric(),
                        // Forms\Components\Select::make('patient_id')
                        //                     ->label('customer')
                        //                     ->relationship('customer', 'id')
                        //                     ->options(function () {
                        //                         return \App\Models\Customer::with('user')
                        //                             ->get()
                        //                             ->pluck('user.name', 'id');
                        //                     })
                        //                     ->searchable()
                        //                     ->required(),
                        Forms\Components\Select::make('treatment_mode')
                            ->options([
                                'housecall' => "House Call",
                                'center'    => "Center"
                            ])
                            ->reactive()
                            ->afterStateHydrated(fn (?PendingAppointment $record, Set $set): ?string => $set('treatment_mode', $record->treatment_mode ?? 'center'))
                            ->afterStateUpdated(function ($state, Set $set){
                                $set('treatment_mode', $state);
                            })
                            ->required(),
                        Forms\Components\Select::make('center_id')
                            ->label('Outlets')
                            ->relationship('center','name')
                            ->options(function () {
                                return Center::take(10)->pluck('name', 'id');
                            })
                            ->searchable()
                            ->visible(fn (Get $get) => $get('treatment_mode') === 'center')
                            ->required(fn (Get $get) => $get('treatment_mode') === 'center'),
                        Forms\Components\TextInput::make('address_1')
                            ->maxLength(255)
                            ->visible(fn (Get $get) => $get('treatment_mode') === 'housecall')
                            ->required(fn (Get $get) => $get('treatment_mode') === 'housecall'),
                        Forms\Components\TextInput::make('address_2')
                            ->maxLength(255)
                            ->visible(fn (Get $get) => $get('treatment_mode') === 'housecall')
                            ->required(fn (Get $get) => $get('treatment_mode') === 'housecall'),
                        Forms\Components\TextInput::make('city')
                            ->maxLength(255)
                            ->visible(fn (Get $get) => $get('treatment_mode') === 'housecall')
                            ->required(fn (Get $get) => $get('treatment_mode') === 'housecall'),
                        Forms\Components\Select::make('state')
                            ->options(['Selangor', 'Kelantan', 'Wilayah Persekutuan', 'Johor Bharu', 'Negeri Sembilan', 'Perak', 'Johor', 'Melaka', 'Kedai', 'Pulau Pinang', 'Perlis', 'Terengganu', 'Pahang', 'Sabah', 'Sarawak'])
                            ->visible(fn (Get $get) => $get('treatment_mode') === 'housecall')
                            ->required(fn (Get $get) => $get('treatment_mode') === 'housecall'),
                        Forms\Components\TextInput::make('postcode')
                            ->maxLength(255)
                            ->visible(fn (Get $get) => $get('treatment_mode') === 'housecall')
                            ->required(fn (Get $get) => $get('treatment_mode') === 'housecall'),
                        Forms\Components\Select::make('preferred_therapist')
                            ->label('Preferred Therapist')
                            ->relationship('therapist','id')
                            ->options(function () {
                                return \App\Models\Therapist::with('user')
                                    ->get()
                                    ->pluck('user.name', 'id');
                            })
                            ->searchable()
                            ->required(),
                        Forms\Components\DatePicker::make('date')->required(),
                        Forms\Components\TimePicker::make('time')->displayFormat('h:i A')->seconds(false)->required(),
                        Forms\Components\TimePicker::make('end_time')->displayFormat('h:i A')->seconds(false)->required(),
                        Forms\Components\Select::make('service_id')
                            ->label('Treatment Type')
                            ->relationship('service','display_name')
                            ->options(function() {
                                return \App\Models\Service::get()->pluck('display_name', 'id');
                            })
                            ->searchable()
                            ->required(),
                        Forms\Components\Select::make('category')
                            ->label('Treatment Category')
                            ->options([
                                "normal",
                                "panel",
                                "pediatric",
                                "geriatric",
                                "member",
                                "OKU",
                                "student",
                                "online",
                            ])
                            ->required(),
                        Forms\Components\Select::make('problem')
                            ->label('Problem Description')
                            ->options([
                                'Back Pain', 'Slip Disc', 'Frozen Shoulder', 'Neck Pain', 'Scoliosis', 'Kyphosis', 'Knee Pain', 'Stroke', 'Other'
                            ])
                            ->columnSpanFull()
                            ->required(),
                        Forms\Components\Textarea::make('note')
                            ->label('Internal Note')
                            ->columnSpanFull(),
                    ]),
                    Forms\Components\Wizard\Step::make('Pricing')->schema([
                        Forms\Components\Select::make('payment_type')
                            ->options([
                                'single' => "Single Purchase",
                                'package' => "Choose 1 Package"
                            ]),
                        Forms\Components\TextInput::make('total_amount')
                            ->maxLength(255),
                        Forms\Components\TextInput::make('payment_method')
                            ->maxLength(255),
                        Forms\Components\TextInput::make('payment_status')
                            ->maxLength(255),
                        Forms\Components\TextInput::make('amount_paid')
                            ->maxLength(255),
                        Forms\Components\TextInput::make('panel')
                            ->maxLength(255),
                        Forms\Components\TextInput::make('coupon_code')
                            ->maxLength(255),


                        Forms\Components\TextInput::make('weekend_charge')
                            ->numeric(),
                        Forms\Components\TextInput::make('night_charge')
                            ->numeric(),
                    ]),
                    Forms\Components\Wizard\Step::make('Confirmation')->schema([
                        Forms\Components\DateTimePicker::make('start_create'),
                        Forms\Components\DateTimePicker::make('end_create'),
                        Forms\Components\TextInput::make('clinic_id')
                            ->numeric(),
                        Forms\Components\TextInput::make('lead_id')
                            ->numeric(),
                    ])
                ])
                ->columnSpanFull(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('booking_id')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('creator_id')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('patient_id')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('status')
                    ->searchable(),
                Tables\Columns\TextColumn::make('address_id')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('address_1')
                    ->searchable(),
                Tables\Columns\TextColumn::make('address_2')
                    ->searchable(),
                Tables\Columns\TextColumn::make('city')
                    ->searchable(),
                Tables\Columns\TextColumn::make('state')
                    ->searchable(),
                Tables\Columns\TextColumn::make('postcode')
                    ->searchable(),
                Tables\Columns\TextColumn::make('date')
                    ->date()
                    ->sortable(),
                Tables\Columns\TextColumn::make('time'),
                Tables\Columns\TextColumn::make('end_time'),
                Tables\Columns\IconColumn::make('recurring')
                    ->boolean(),
                Tables\Columns\TextColumn::make('parents_id')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('treatment_mode'),
                Tables\Columns\TextColumn::make('source')
                    ->searchable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('preferred_therapist')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('center_id')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('payment_method')
                    ->searchable(),
                Tables\Columns\TextColumn::make('payment_status')
                    ->searchable(),
                Tables\Columns\TextColumn::make('amount_paid')
                    ->searchable(),
                Tables\Columns\TextColumn::make('panel')
                    ->searchable(),
                Tables\Columns\TextColumn::make('coupon_code')
                    ->searchable(),
                Tables\Columns\TextColumn::make('total_amount')
                    ->searchable(),
                Tables\Columns\TextColumn::make('extra_charge')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('weekend_charge')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('night_charge')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('service_id')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('type')
                    ->searchable(),
                Tables\Columns\TextColumn::make('start_create')
                    ->dateTime()
                    ->sortable(),
                Tables\Columns\TextColumn::make('end_create')
                    ->dateTime()
                    ->sortable(),
                Tables\Columns\TextColumn::make('category')
                    ->searchable(),
                Tables\Columns\TextColumn::make('clinic_id')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('lead_id')
                    ->numeric()
                    ->sortable(),
            ])
            ->filters([
                // Tables\Filters\TrashedFilter::make(),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                    // Tables\Actions\ForceDeleteBulkAction::make(),
                    // Tables\Actions\RestoreBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListPendingAppointments::route('/'),
            'create' => Pages\CreatePendingAppointment::route('/create'),
            'view' => Pages\ViewPendingAppointment::route('/{record}'),
            'edit' => Pages\EditPendingAppointment::route('/{record}/edit'),
        ];
    }

    public static function shouldRegisterNavigation(): bool
    {
        return false;
    }

}
