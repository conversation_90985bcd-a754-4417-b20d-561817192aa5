<?php

namespace App\Filament\Resources;

use App\Filament\Resources\LeadsResource\Pages;
use App\Filament\Resources\LeadsResource\RelationManagers;
use App\Filament\Resources\LeadsResource\RelationManagers\PendingAppointmentsRelationManager;
use App\Models\Center;
use App\Models\Customer;
use App\Models\Lead;
use App\Models\PendingAppointment;
use App\User;
use Closure;
use DateTime;
use Filament\Actions\Action;
use Filament\Forms;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\DateTimePicker;
use Filament\Forms\Components\FileUpload;
use Filament\Forms\Components\Group;
use Filament\Forms\Components\Radio;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Forms\Get;
use Filament\Forms\Set;
use Filament\Resources\Resource;
use Filament\Support\RawJs;
use Filament\Tables;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Table;
use Filament\Widgets\StatsOverviewWidget\Stat;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Malzariey\FilamentDaterangepickerFilter\Filters\DateRangeFilter;
use Ysfkaya\FilamentPhoneInput\Forms\PhoneInput;
use Ysfkaya\FilamentPhoneInput\PhoneInputNumberType;

use function PHPSTORM_META\map;

class LeadsResource extends Resource
{
    protected static ?string $model = Lead::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    protected static array $locations = [
                        'SA' => 'SA',
                        'WM' => 'WM',
                        'KD' =>'KD',
                        'AH' => 'AH',
                        'BBB' => 'BBB',
                        'KS' => 'KS',
                        'BPB' => 'BPB',
                        'KRUBONG' => 'KRUBONG',
                        'BBK' => 'BBK',
                        'BTHO' => 'BTHO',
                        'SK' => 'SK',
                        'ECO' => 'ECO',
                        'JP' => 'JP',
                        'Partners' => 'Partners',
                        'TOPPEN' => 'Toppen',
                        'OUT OF AREA' => 'Out Of Area'
                    ];
    protected static array $resources = [
                        'Inquiry-Web' => 'Inquiry Web',
                        'Direct-Whatsapp' => 'Whatsapp Business',
                        'Direct-Call' => 'Direct Call',
                        'META-Leads-Form' => 'META Leads Form',
                        'Cash-Leads-Form' => 'Cash Leads Form',
                        'Existing-Cust-Form' => 'Existing Cust Form',
                        'Tiktok-Leads-Form' => 'Tiktok Leads Form',
                        'Panel-Leads-Form' => 'Panel Leads Form',
                        'Friend-and-Family' => 'Friend & Family',
                        'Corporate-Event' => 'Corporate Event',
                        'Google' => 'Google',
                        'Broadcast' => 'Broadcast',
                        'Live-Chat' => 'Live Chat',
                        'Birthday-Wish' => 'Birthday Wish'
                    ];
    protected static array $problems = [
                            "Sudden Pain (Tiba-tiba rasa sakit kuat)",
                            "Back or Neck Pain (Sakit belakang / tengkuk)",
                            "Muscle or Joint Stiffness (Rasa tegang atau keras)",
                            "Sports Injury (Cedera waktu bersukan)",
                            "Workplace Injury (Kecederaan masa kerja)",
                            "Dislocated Joint (Sendi terkehel)",
                            "Bone Fracture Recovery (Patah tulang – dalam proses sembuh)",
                            "Tendon/Ligament Injury (Cedera ligamen atau urat – contoh",
                            "Post-Surgery Rehab / Pemulihan Selepas Pembedahan",
                            "After Joint Surgery (Lepas pembedahan lutut / pinggul / tangan)",
                            "After Spine Surgery (Selepas operasi tulang belakang)",
                            "After Fracture Surgery (Lepas patah tulang letak besi/pin)",
                            "Stroke Recovery (Pemulihan lepas strok / angin ahmar)",
                            "Nerve Injury (Kecederaan saraf – contoh",
                            "Elderly & Mobility Issues / Warga Emas & Masalah Bergerak",
                            "Difficulty Walking or Moving (Susah berjalan atau bergerak)",
                            "After Hospital Discharge (Lepas keluar hospital – perlukan rehab)",
                            "Fall Prevention Program (Program cegah jatuh)",
                            "Developmental Delay (Lambat merangkak / berjalan)",
                            "Pediatric Conditions (Masalah otot atau sendi – contoh",
                            "Infant Treatment (Rawatan bayi – contoh kaki bengkok)",
                            "Postnatal Recovery (Sakit badan lepas bersalin)",
                            "Pelvic Floor Issues (Masalah kawalan kencing / otot pelvis)",
                            "Diastasis Recti (Otot perut terbuka lepas bersalin)",
                            "Others"
                        ];


    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make()
                    ->schema([
                        Select::make('form_type')
                            ->options([
                                'normal' => 'Normal',
                                'event' => 'Event'
                            ])
                            ->columnSpanFull()
                            ->reactive()
                            ->afterStateHydrated(fn (?Lead $record, Set $set): ?string => $set('form_type', $record->form_type ?? 'normal'))
                            ->afterStateUpdated(function ($state, Set $set){
                                $set('form_type', $state);
                            })
                            ->required(),
                        ])
                    ->columnSpan(2),
                Forms\Components\Section::make('Event Form')
                    ->schema(static::getDetailFormEvent())
                    ->visible(fn (Get $get) => $get('form_type') === 'event')
                    ->columnSpan(2),
                Forms\Components\Section::make('Normal Form')
                    ->schema(static::getDetailFormNormal())
                    ->visible(fn (Get $get) => $get('form_type') === 'normal')
                    ->columnSpan(2),
                Forms\Components\Section::make('Appt Information')
                    ->schema([
                        Forms\Components\Group::make()
                            ->relationship('pendingAppointment')
                            ->schema(static::getCreateApptForm())
                    ])
                    ->visible(fn(Get $get) => $get('status') === 'Closed')
                    ->columnSpan(2),
                Forms\Components\Section::make()
                    ->schema([
                        Forms\Components\Group::make()
                        ->schema([
                            Forms\Components\Placeholder::make('created_at')
                            ->label('Created at')
                            ->content(fn (Lead $record): ?string => $record->created_at?->diffForHumans()),

                            Forms\Components\Placeholder::make('updated_at')
                                ->label('Last modified at')
                                ->content(fn (Lead $record): ?string => $record->updated_at?->diffForHumans()),

                            Forms\Components\Placeholder::make('closed_at')
                                ->label('Closed at')
                                ->content(fn (Lead $record): ?string => $record->closed_at?->diffForHumans()),

                            Forms\Components\Placeholder::make('closed_by')
                                ->label('PIC')
                                ->content(fn (Lead $record): ?string => $record->dealer?->name),
                        ]),

                    ])
                    ->columnSpan(1)
                    ->hidden(fn (?Lead $record) => $record === null),
            ])
            ->columns(3);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->searchable(),
                Tables\Columns\TextColumn::make('phone')
                    ->label('WhatsApp')
                    ->formatStateUsing(fn ($state) => '+'.preg_replace('/\D/', '', $state)) // sanitize phone number
                    ->url(fn ($state) => "https://wa.me/" . preg_replace('/\D/', '', $state), true)
                    ->openUrlInNewTab()
                    ->color('success')
                    ->searchable(),
                Tables\Columns\TextColumn::make('source')
                    ->searchable(),
                Tables\Columns\TextColumn::make('customer_type'),
                Tables\Columns\IconColumn::make('is_existing_customer')
                    ->label('Existing Customer')
                    ->boolean(),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: false),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                SelectFilter::make('leads_category')->options([
                            'new-leads' => 'New Leads',
                            'fu-leads' => 'FU Leads'
                ]),
                SelectFilter::make('branch')->options(self::$locations),
                SelectFilter::make('source')->options(self::$resources),
                SelectFilter::make('created_by')
                    ->multiple()
                    ->placeholder('Choose Lead Createor ')
                    ->options(User::take(5)->pluck('name', 'id'))
                    ->relationship('creator', 'name')
                    ->searchable(),

                // 5) By Follow Up Session
                // 8) By Contact Medium
                DateRangeFilter::make('created_at'),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
            ])
            ->defaultSort('created_at', 'desc')
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            // PendingAppointmentsRelationManager::class
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListLeads::route('/'),
            'create' => Pages\CreateLeads::route('/create'),
            'view' => Pages\ViewLeads::route('/{record}'),
            'edit' => Pages\EditLeads::route('/{record}/edit'),
        ];
    }

    public static function getDetailFormEvent() : array{
        return [
            Forms\Components\TextInput::make('name')
                    ->maxLength(200)
                    ->required()
                    ->columnSpanFull(),
                    PhoneInput::make('phone')->columnSpanFull()->required(),
                    TextInput::make('email')
                        ->email()
                        ->columnSpanFull()
                        ->required(),
                    Textarea::make('address')
                        ->columnSpanFull()
                        ->required(),
                    TextInput::make('company_name')->required(),
                    TextInput::make('job_title'),
                    TextInput::make('department'),
                    Select::make('condition')
                        ->label('Condition / Problem')
                        ->options(self::$problems)
                        ->required()
                        ->columnSpanFull(),
                    TextInput::make('other_condition')
                        ->label('If none of the above, please describe your problem'),
                    Radio::make('is_ever_visited')
                        ->label('Have you ever come to Physiomobile in seeking treatment?')
                        ->boolean()
                        ->required()

        ];
    }

    public static function getDetailFormNormal() : array {
        return [
            Forms\Components\Select::make('customer_type')
                ->options([
                    "New Patient" => "New Patient",
                    "Re-Subscribers" => "Re-Subscribers",
                    "Upgrade Patient" => "Upgrade Patient"
                ])
                ->default('New Patient')
                ->required(),
            Forms\Components\Toggle::make('is_existing_customer')
                ->label("Existing Customer")
                ->default(false)
                ->reactive()
                ->afterStateHydrated(function (?Lead $record, callable $set) {
                    if(isset($record->is_existing_customer)){
                        $set('is_existing_customer', $record->is_existing_customer === 1 ? true : false);
                    }

                    $set('is_existing_customer', false);
                })
                ->afterStateUpdated(function ($state, callable $set){
                    $set('is_existing_customer', $state);
                }),
            Forms\Components\Select::make('customer_id')
                ->searchable()
                ->options(function () {
                    return User::whereHas('customer')
                                ->with('customer')
                                ->take(10)
                                ->get()
                                ->mapWithKeys(function ($user) {
                                    return [$user->customer->id => $user->name];
                                });;
                })
                ->getSearchResultsUsing(function (string $search) {
                    return User::whereHas('customer')
                                ->with('customer')
                                ->where('name', 'like', "%{$search}%")
                                ->take(10)
                                ->get()
                                ->mapWithKeys(function ($user) {
                                    return [$user->customer->id => $user->name];
                                });;
                })
                ->getOptionLabelUsing(function ($value): ?string {
                    $customer = \App\Models\Customer::with('user')->find($value);
                    return $customer?->user?->name;
                })
                ->label('Search Name')
                ->reactive()
                ->required(fn (Get $get) => $get('is_existing_customer') === true)
                ->visible(fn (Get $get) => $get('is_existing_customer') === true)
                ->afterStateUpdated(function ($state, callable $set, Get $get){
                    if ($get('is_existing_customer') === true) {
                        $customerId = $state;
                        $customer = Customer::find($customerId);

                        $user = User::find($customer->user_id);
                        $set('name', $user->name);
                        $set('email', $user->email);
                        $set('phone', $user->mobile);
                    }
                })
                ->columnSpanFull(),
            Forms\Components\TextInput::make('name')
                ->maxLength(200)
                ->required(fn (Get $get) => $get('is_existing_customer') === false)
                ->readOnly(fn (Get $get) => $get('is_existing_customer') === true)
                ->columnSpanFull(),
            PhoneInput::make('phone')
                ->onlyCountries(['my','id','sg'])
                ->defaultCountry('MYS')
                // ->disabled(fn (Get $get) => $get('is_existing_customer') === true)
                ->columnSpanFull(),
            Forms\Components\TextInput::make('nric')
                ->label('NRIC')
                ->numeric()
                ->minLength(12)
                ->maxLength(12)
                 ->visible(fn (Get $get) => $get('is_existing_customer') === false)
                ->columnSpanFull(),
            Textarea::make('address')
                ->visible(fn (Get $get) => $get('is_existing_customer') === false)
                ->columnSpanFull(),
            Forms\Components\Select::make('source')
                ->label('Resources')
                ->options(self::$resources)
                ->required()
                ->searchable()
                ->columnSpanFull(),
            Select::make('conditions')
                ->label('Condition / Problem')
                ->options(self::$problems)
                ->multiple()
                ->searchable()
                ->required()
                ->columnSpanFull(),
            Select::make('status')
                ->label('Leads Status')
                ->options([
                    'Not Closed' => 'Not Closed',
                    'In Progress' => 'In progress',
                    'Pending' => 'Pending'
                ])
                ->required()
                ->columnSpanFull()
                ->reactive()
                ->afterStateHydrated(fn (?Lead $record, callable $set): ?string => $set('status', $record->status ?? ''))
                ->afterStateUpdated(function ($state, callable $set){
                    $set('status', $state);
                    if($state === 'Closed'){
                        $set('closed_by', auth()->id());
                        $set('closed_at', now());
                    }else{
                        $set('closed_by',null);
                        $set('closed_at', null);
                    }
                })
                ->visible(fn(Get $get) => $get('status') !== 'Closed'),
            Forms\Components\Textarea::make('internal')
                ->label('Internal Notes')
                ->columnSpanFull(),
            FileUpload::make('uploaded_file_reference')
                ->disk('spaces')
                ->directory('leads-file-references')
                ->downloadable()
                ->openable()
                ->columnSpanFull(),
        ];
    }

    public static function getClosedLeadForm(): array{
        return [
            Radio::make('customer_type')
                        ->options([
                            'New Patient' => 'New Patient',
                            'Re- Subscribers' => 'Re- Subscribers'
                        ])
                        ->columnSpanFull()
                        ->required(fn(callable $get) => $get('status') === 'Closed')
                        ->visible(fn(callable $get) => $get('status') === 'Closed'),
                    Radio::make('service_type')
                        ->options([
                            'center' => 'Center',
                            'housecall' => 'Housecall'
                        ])
                        ->columnSpanFull()
                        ->required(fn(callable $get) => $get('status') === 'Closed')
                        ->visible(fn(callable $get) => $get('status') === 'Closed'),
                    Forms\Components\Select::make('branch')
                        ->label('Outlets')
                        ->options(self::$locations)
                        ->searchable()
                        ->columnSpanFull()
                        ->required(fn(callable $get) => $get('status') === 'Closed')
                        ->visible(fn(callable $get) => $get('status') === 'Closed'),
                    Select::make('package_closed')
                        ->options([
                            'CU' => 'CU',
                            'TSS' => 'TSS',
                            'TPR' => 'TPR',
                            'TPA' => 'TPA',
                            'TPB' => 'TPB',
                            'TPC' => 'TPC',
                            'TPD' => 'TPD',
                            'PANEL' => 'PANEK',
                            'Extend-Validity' => 'Extend Validity',
                            'SS-FBTM' => 'SS FBTM',
                            'SS-FATM' => 'SS FATM',
                            'P-FBTM' => 'P FBTM',
                            'Panel-FBTM' => 'Panel FBTM',
                            'Panel-FATM' => 'Panel FATM',
                        ])
                        ->columnSpanFull()
                        ->required(fn(callable $get) => $get('status') === 'Closed')
                        ->visible(fn(callable $get) => $get('status') === 'Closed'),
                    TextInput::make('invoice_number')
                        ->required(fn(callable $get) => $get('status') === 'Closed')
                        ->visible(fn(callable $get) => $get('status') === 'Closed'),
                    TextInput::make('amount')
                        ->label('Sales (RM)')
                        ->default(0)
                        ->numeric()
                        ->step('any')
                        ->stripCharacters(',')
                        ->mask(RawJs::make('$money($input)'))
                        ->columnSpanFull()
                        ->required(fn(callable $get) => $get('status') === 'Closed')
                        ->visible(fn(callable $get) => $get('status') === 'Closed'),
                    Radio::make('payment')
                        ->label("Payment Status")
                        ->options([
                            'full' => 'Full',
                            'partial' => 'Partial',
                            'panel' => 'Panel',
                            'deposit' => 'Deposit'
                        ])
                        ->columnSpanFull()
                        ->required(fn(callable $get) => $get('status') === 'Closed')
                        ->visible(fn(callable $get) => $get('status') === 'Closed'),
                    TextInput::make('receipt_number')
                        ->required(fn(callable $get) => $get('status') === 'Closed')
                        ->visible(fn(callable $get) => $get('status') === 'Closed'),
                    DateTimePicker::make('preferred_appointment_date_time')
                        ->label('Preferred Appointment Date & Time')
                        ->columnSpanFull()
                        ->visible(fn(callable $get) => $get('status') === 'Closed'),
                    Select::make('preferred_therapist_id')
                        ->label('Preferred Therapist')
                        ->required()
                        ->relationship(name: 'therapist.user', titleAttribute: 'name')
                        ->visible(fn(callable $get) => $get('status') === 'Closed')
                        ->searchable(),
                    TextInput::make('collection')
                                ->label('Collection (RM)')
                                ->default(0)
                                ->numeric()
                                ->step('any')
                                ->stripCharacters(',')
                                ->mask(RawJs::make('$money($input)'))
                                ->columnSpanFull()
                                ->required(fn(callable $get) => $get('status') === 'Closed')
                                ->visible(fn(callable $get) => $get('status') === 'Closed'),
        ];
    }

    public static function getCreateApptForm(): array {
        return [
            Forms\Components\Select::make('treatment_mode')
                ->options([
                    'housecall' => "House Call",
                    'center'    => "Center"
                ])
                ->default('center')
                ->reactive()
                ->afterStateHydrated(fn (?PendingAppointment $record, Set $set): ?string => $set('treatment_mode', $record->treatment_mode ?? 'center'))
                ->afterStateUpdated(function ($state, Set $set){
                    $set('treatment_mode', $state);
                })
                ->required(),
            Forms\Components\Select::make('center_id')
                ->label('Outlets')
                ->relationship('center','name')
                ->options(function () {
                    return Center::take(10)->pluck('name', 'id');
                })
                ->searchable()
                ->visible(fn (Get $get) => $get('treatment_mode') === 'center')
                ->required(fn (Get $get) => $get('treatment_mode') === 'center'),
            Forms\Components\TextInput::make('address_1')
                ->maxLength(255)
                ->visible(fn (Get $get) => $get('treatment_mode') === 'housecall')
                ->required(fn (Get $get) => $get('treatment_mode') === 'housecall'),
            Forms\Components\TextInput::make('address_2')
                ->maxLength(255)
                ->visible(fn (Get $get) => $get('treatment_mode') === 'housecall')
                ->required(fn (Get $get) => $get('treatment_mode') === 'housecall'),
            Forms\Components\TextInput::make('city')
                ->maxLength(255)
                ->visible(fn (Get $get) => $get('treatment_mode') === 'housecall')
                ->required(fn (Get $get) => $get('treatment_mode') === 'housecall'),
            Forms\Components\Select::make('state')
                ->options(['Selangor', 'Kelantan', 'Wilayah Persekutuan', 'Johor Bharu', 'Negeri Sembilan', 'Perak', 'Johor', 'Melaka', 'Kedai', 'Pulau Pinang', 'Perlis', 'Terengganu', 'Pahang', 'Sabah', 'Sarawak'])
                ->visible(fn (Get $get) => $get('treatment_mode') === 'housecall')
                ->required(fn (Get $get) => $get('treatment_mode') === 'housecall'),
            Forms\Components\TextInput::make('postcode')
                ->maxLength(255)
                ->visible(fn (Get $get) => $get('treatment_mode') === 'housecall')
                ->required(fn (Get $get) => $get('treatment_mode') === 'housecall'),
            Forms\Components\Select::make('preferred_therapist')
                ->label('Preferred Therapist')
                ->relationship('therapist','id')
                ->options(function () {
                    return \App\Models\Therapist::with('user')
                        ->get()
                        ->pluck('user.name', 'id');
                })
                ->searchable()
                ->required(),
            Forms\Components\DatePicker::make('date')->required(),
            Forms\Components\TimePicker::make('time')->displayFormat('h:i A')->seconds(false)->required(),
            Forms\Components\TimePicker::make('end_time')->displayFormat('h:i A')->seconds(false)->required(),
            Forms\Components\Select::make('service_id')
                ->label('Treatment Type')
                ->relationship('service','display_name')
                ->options(function() {
                    return \App\Models\Service::get()->pluck('display_name', 'id');
                })
                ->searchable()
                ->required(),
            Forms\Components\Select::make('category')
                ->label('Treatment Category')
                ->options([
                    "normal",
                    "panel",
                    "pediatric",
                    "geriatric",
                    "member",
                    "OKU",
                    "student",
                    "online",
                ])
                ->required(),
            Forms\Components\Select::make('problem')
                ->label('Problem Description')
                ->options([
                    'Back Pain', 'Slip Disc', 'Frozen Shoulder', 'Neck Pain', 'Scoliosis', 'Kyphosis', 'Knee Pain', 'Stroke', 'Other'
                ])
                ->columnSpanFull()
                ->required(),
            Forms\Components\Textarea::make('note')
                ->label('Internal Note')
                ->columnSpanFull(),
            Forms\Components\Placeholder::make('custom_button')
                ->label('Continue Booking')
                ->content(fn ($record) => view('components.button.redirect-appt-stage-two', ['id' => $record?->id])),
        ];
    }

}
