<?php

namespace App\Filament\Pages;

use Filament\Pages\Page;

class CustomReport extends Page
{
    protected static ?string $navigationIcon = 'heroicon-o-document-text';
    protected static string $view = 'filament.pages.custom-report';
    protected static ?string $navigationLabel = 'Internal References';
    protected static ?string $title = 'Internal References';

    // Optional: restrict access
    // public static function canAccess(): bool
    // {
    //     return auth()->user()?->hasRole('admin');
    // }
}


