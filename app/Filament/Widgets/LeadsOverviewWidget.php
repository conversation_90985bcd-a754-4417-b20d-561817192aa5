<?php

namespace App\Filament\Widgets;

use App\Models\Lead;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;

class LeadsOverviewWidget extends BaseWidget
{
    protected function getStats(): array
    {
        $totalLeads = Lead::query()->count();
        $totalClosed = Lead::query()->where('status', 'Closed')->count();

        return [
            Stat::make("Total Leads", number_format(Lead::query()->count(), 0, ',', '.')),
            Stat::make("Total Closing", number_format(Lead::query()->where('status', 'Closed')->count(), 0, ',', '.')),
            Stat::make('Closing Rate', ($totalClosed / $totalLeads) * 100 . '%')
        ];
    }
}
