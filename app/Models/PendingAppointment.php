<?php

namespace App\Models;

use App\User;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class PendingAppointment extends Model
{
    protected $fillable = [
        'lead_id',
        'booking_id',
        'creator_id',
        'patient_id',
        'status',
        'address_id',
        'address_1',
        'address_2',
        'city',
        'state',
        'postcode',
        'date',
        'time',
        'end_time',
        'recurring',
        'parents_id',
        'treatment_mode',
        'source',
        'preferred_therapist',
        'center_id',
        'payment_method',
        'payment_status',
        'amount_paid',
        'panel',
        'coupon_code',
        'total_amount',
        'extra_charge',
        'weekend_charge',
        'night_charge',
        'service_id',
        'type',
        'start_create',
        'end_create',
        'category',
        'clinic_id',
        'problem'
    ];

    public function lead()
    {
        return $this->belongsTo(Lead::class, 'lead_id');
    }

    public function therapist()
    {
        return $this->belongsTo(Therapist::class, 'preferred_therapist');
    }

    public function customer()
    {
        return $this->belongsTo(Customer::class);
    }

    public function creator()
    {
        return $this->belongsTo(User::class);
    }

    public function center()
    {
        return $this->belongsTo(Center::class);
    }

    public function service ()
    {
        return $this->belongsTo(Service::class);
    }
}
