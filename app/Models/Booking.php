<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Booking extends Model
{
    const BOOKING_SUBMITTED  = 100;
    const BOOKING_CANCELLED = 150;
    const BOOKING_POSTPONED = 160;
    const BOOKING_THERAPIST_ASSIGNED = 200;
    const BOOKING_ON_JOURNEY = 201;
    const BOOKING_CHECKED_IN = 202;
    const BOOKING_STARTED = 250;
    const BOOKING_COMPLETED = 251;
    const BOOKING_REPORT_SUBMITTED = 252;

    const STATUSES = [
        'BOOKING_SUBMITTED' => Booking::BOOKING_SUBMITTED,
        'BOOKING_CANCELLED' => Booking::BOOKING_CANCELLED,
        'BOOKING_POSTPONED' => Booking::BOOKING_POSTPONED,
        'BOOKING_THERAPIST_ASSIGNED' => Booking::BOOKING_THERAPIST_ASSIGNED,
        'BOOKING_ON_JOURNEY' => Booking::BOOKING_ON_JOURNEY,
        'BOOKING_CHECKED_IN' => Booking::BOOKING_CHECKED_IN,
        'BOOKING_STARTED' => Booking::BOOKING_STARTED,
        'BOOKING_COMPLETED' => Booking::BOOKING_COMPLETED,
        'BOOKING_REPORT_SUBMITTED' => Booking::BOOKING_REPORT_SUBMITTED,
    ];

    public function therapist()
    {
        return $this->belongsToMany(Therapist::class);
    }

    public function getStatusSlugSpanAttribute() {

        if ($this->status == Booking::BOOKING_THERAPIST_ASSIGNED) {
            $class = 'success';
        } else if ($this->status == Booking::BOOKING_CANCELLED) {
            $class = 'danger';
        } else {
            $class = 'warning';
        }


        foreach (Booking::STATUSES as $statusSlug => $statusValue) {
            if ($statusValue == $this->status) {
                return $statusSlug;
            }
        }
    }
}
