<?php

namespace App\Models;

use App\User;
use Illuminate\Database\Eloquent\Model;

class Customer extends Model
{
    protected $table = "patients";

    protected $fillable = [
        'status',
        'company',
        'user_id',
        'source',
        'medium',
    ];

    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    public function bookings(){
        return $this->hasMany(Booking::class, 'patient_id', 'id');
    }

    public function latestBooking(){
        return $this->hasOne(Booking::class, 'patient_id')->latestOfMany('date');
    }

    public function getTotalBookingAttribute()
    {
        // Where Not In CANCELLED or POSPONED
        return $this->bookings()->where('status', '>=', 200)->count();
    }
}
