<?php

namespace App\Models;

use App\User;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;

use function PHPSTORM_META\map;

class Lead extends Model
{
    protected $fillable = [
        'name',
        'email',
        'phone',
        'branch',
        'source',
        'meta',
        'message',
        'nric',
        'leads_category',
        'follow_up',
        'follow_up_date',
        'condition',
        'address',
        'status',
        'customer_type',
        'service_type',
        'package_closed',
        'invoice_number',
        'payment',
        'amount',
        'collection',
        'appointment_date',
        'uploaded_file_reference',
        'receipt_number',
        'preferred_appointment_date_time',
        'preferred_therapist_id',
        'created_by',
        'company_name',
        'job_title',
        'department',
        'other_condition',
        'is_ever_visited',
        'form_type',
        'pending_appointment_id',
        'closed_at',
        'closed_by',
        'conditions',
        'customer_id',
        'is_existing_customer',
    ];

    protected $casts = [
        'conditions' => 'array',
        'closed_at' => 'date'
    ];

    public function therapist(){
        return $this->belongsTo(Therapist::class, 'preferred_therapist_id');
    }

    public function creator(){
        return $this->belongsTo(User::class, 'created_by');
    }

    public function pendingAppointment(){
        return $this->belongsTo(PendingAppointment::class);
    }

    public function dealer(){
        return $this->belongsTo(User::class, 'closed_by');
    }

    public function customer(){
        return $this->belongsTo(Customer::class, 'customer_id');
    }

    public static function boot(){

        parent::boot();

        static::creating(function ($lead) {
            $lead->leads_category = 'new-leads';
            $lead->created_by = auth()->id();
        });

        static::addGlobalScope('closedByUser', function (Builder $builder) {
            // It means to show closed leads only to closed by
            // $builder->where('status', '!=', 'Closed');
            // $builder->orWhere('closed_by', auth()->id());
        });
    }
}
