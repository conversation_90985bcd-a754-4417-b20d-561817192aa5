<?php

namespace App\Webhook;

// use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Request;
use Spatie\WebhookClient\SignatureValidator\SignatureValidator;
use Spatie\WebhookClient\Models\WebhookCall;
use Spatie\WebhookClient\WebhookConfig;

class SimpleHeaderValidator implements SignatureValidator
{
    public function isValid(Request $request, WebhookConfig $config): bool
    {
        return $request->headers->get('X-Signature') === $config->signingSecret;
    }
}
